<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AffiliateFlow - Sistema Completo de Afiliação Digital</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Descubra como faturar R$ 15.000/mês com afiliação digital usando nosso sistema completo e automatizado. Treinamento exclusivo + ferramentas profissionais.">
    <meta name="keywords" content="afiliação digital, marketing digital, renda extra, trabalhar online, sistema automatizado">
    <meta name="author" content="AffiliateFlow">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://affiliateflow.com/">
    <meta property="og:title" content="AffiliateFlow - Fature R$ 15k/mês com Afiliação Digital">
    <meta property="og:description" content="Sistema completo para você começar a faturar com afiliação digital hoje mesmo. Treinamento + ferramentas + suporte.">
    <meta property="og:image" content="https://affiliateflow.com/og-image.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://affiliateflow.com/">
    <meta property="twitter:title" content="AffiliateFlow - Fature R$ 15k/mês com Afiliação Digital">
    <meta property="twitter:description" content="Sistema completo para você começar a faturar com afiliação digital hoje mesmo.">
    <meta property="twitter:image" content="https://affiliateflow.com/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.emailjs.com">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS (CDN for testing - replace with built CSS in production) -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #f59e0b;
            --accent: #10b981;
            --dark: #0f172a;
            --light: #f8fafc;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f8fafc;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #6366f1 0%, #f59e0b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #f59e0b 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
        }
        
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading" class="fixed inset-0 bg-slate-900 flex items-center justify-center z-50">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
            <p class="text-slate-300">Carregando AffiliateFlow...</p>
        </div>
    </div>

    <!-- Main Content -->
    <div id="app" class="hidden">
        <!-- Header -->
        <header class="relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-indigo-900/20 to-amber-900/20"></div>
            <div class="relative container mx-auto px-4 py-20">
                <div class="text-center max-w-4xl mx-auto">
                    <!-- Logo/Brand -->
                    <div class="mb-8">
                        <h1 class="text-5xl md:text-7xl font-black mb-4">
                            <span class="gradient-text">AffiliateFlow</span>
                        </h1>
                        <p class="text-xl text-slate-300">Sistema Completo de Afiliação Digital</p>
                    </div>
                    
                    <!-- Main Headline -->
                    <h2 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                        Fature <span class="gradient-text">R$ 15.000/mês</span><br>
                        com Afiliação Digital
                    </h2>
                    
                    <!-- Subheadline -->
                    <p class="text-xl md:text-2xl text-slate-300 mb-8 leading-relaxed">
                        Sistema 100% automatizado + treinamento completo + ferramentas profissionais.<br>
                        <strong class="text-amber-400">Comece hoje mesmo, mesmo sendo iniciante!</strong>
                    </p>
                    
                    <!-- CTA Button -->
                    <div class="mb-12">
                        <button onclick="scrollToForm()" class="btn-primary text-white font-bold py-4 px-8 rounded-full text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300">
                            🚀 QUERO COMEÇAR AGORA - GRÁTIS
                        </button>
                        <p class="text-sm text-slate-400 mt-3">
                            ⚡ Últimas 7 vagas disponíveis • 100% gratuito para começar
                        </p>
                    </div>
                    
                    <!-- Social Proof -->
                    <div class="glass-effect rounded-2xl p-6 max-w-2xl mx-auto">
                        <div class="flex items-center justify-center space-x-8 text-center">
                            <div>
                                <div class="text-2xl font-bold text-amber-400">2.847+</div>
                                <div class="text-sm text-slate-300">Alunos Ativos</div>
                            </div>
                            <div class="h-8 w-px bg-slate-600"></div>
                            <div>
                                <div class="text-2xl font-bold text-green-400">R$ 8.2M+</div>
                                <div class="text-sm text-slate-300">Faturamento Total</div>
                            </div>
                            <div class="h-8 w-px bg-slate-600"></div>
                            <div>
                                <div class="text-2xl font-bold text-indigo-400">98.7%</div>
                                <div class="text-sm text-slate-300">Satisfação</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Lead Capture Form -->
        <section id="lead-form" class="py-20 bg-gradient-to-r from-indigo-900/30 to-purple-900/30">
            <div class="container mx-auto px-4">
                <div class="max-w-2xl mx-auto">
                    <div class="glass-effect rounded-3xl p-8 text-center">
                        <h3 class="text-3xl font-bold mb-4">
                            🎯 Garanta Sua Vaga <span class="gradient-text">GRATUITA</span>
                        </h3>
                        <p class="text-slate-300 mb-8">
                            Preencha os dados abaixo e receba acesso imediato ao sistema completo + bônus exclusivos
                        </p>
                        
                        <form id="leadForm" class="space-y-4">
                            <input 
                                type="text" 
                                name="name" 
                                placeholder="Seu nome completo" 
                                required
                                class="w-full px-4 py-3 rounded-lg bg-slate-800 border border-slate-600 text-white placeholder-slate-400 focus:border-indigo-500 focus:outline-none"
                            >
                            <input 
                                type="email" 
                                name="email" 
                                placeholder="Seu melhor e-mail" 
                                required
                                class="w-full px-4 py-3 rounded-lg bg-slate-800 border border-slate-600 text-white placeholder-slate-400 focus:border-indigo-500 focus:outline-none"
                            >
                            <input 
                                type="tel" 
                                name="phone" 
                                placeholder="Seu WhatsApp (com DDD)" 
                                required
                                class="w-full px-4 py-3 rounded-lg bg-slate-800 border border-slate-600 text-white placeholder-slate-400 focus:border-indigo-500 focus:outline-none"
                            >
                            
                            <button 
                                type="submit" 
                                class="w-full btn-primary text-white font-bold py-4 px-8 rounded-lg text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300"
                            >
                                🚀 GARANTIR MINHA VAGA GRATUITA
                            </button>
                        </form>
                        
                        <div class="mt-6 text-sm text-slate-400">
                            <p>🔒 Seus dados estão 100% seguros • ✅ Sem spam • 🎁 Bônus exclusivos inclusos</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="py-12 bg-slate-900/50">
            <div class="container mx-auto px-4 text-center">
                <div class="mb-6">
                    <h3 class="text-2xl font-bold gradient-text mb-2">AffiliateFlow</h3>
                    <p class="text-slate-400">Transformando vidas através da afiliação digital</p>
                </div>
                
                <div class="text-sm text-slate-500">
                    <p>&copy; 2025 AffiliateFlow. Todos os direitos reservados.</p>
                    <p class="mt-2">
                        <a href="#" class="hover:text-slate-300">Política de Privacidade</a> • 
                        <a href="#" class="hover:text-slate-300">Termos de Uso</a> • 
                        <a href="#" class="hover:text-slate-300">Contato</a>
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script>
        // Remove loading screen
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('app').classList.remove('hidden');
            }, 1000);
        });

        // Smooth scroll to form
        function scrollToForm() {
            document.getElementById('lead-form').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        // Form submission
        document.getElementById('leadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                source: 'landing-page',
                timestamp: new Date().toISOString()
            };
            
            // Simulate form submission
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '⏳ Processando...';
            button.disabled = true;
            
            setTimeout(() => {
                alert('✅ Parabéns! Sua vaga foi garantida com sucesso!\n\nVerifique seu e-mail para acessar o sistema completo.');
                button.innerHTML = '✅ VAGA GARANTIDA!';
                button.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                
                // Reset form
                setTimeout(() => {
                    this.reset();
                    button.innerHTML = originalText;
                    button.disabled = false;
                    button.style.background = '';
                }, 3000);
            }, 2000);
        });

        // Analytics tracking (placeholder)
        function trackEvent(event, data) {
            console.log('Event tracked:', event, data);
            // Implement Google Analytics, Facebook Pixel, etc.
        }

        // Track page view
        trackEvent('page_view', { page: 'landing' });
    </script>
</body>
</html>
